<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="https://static.yunzhijia.com/public/js/qing/latest/qing.js"></script>
    <title></title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Arial", "Helvetica", sans-serif;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        height: auto;
      }

      /* 主容器 */
      .agent-container {
        padding: 16px 4%;
        min-height: 100vh;
        height: auto;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        background: linear-gradient(145deg, #ffffff 0%, #c5e1fa 100%);
        background-image: url("./image/agent_bg.png");
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center top;
        background-attachment: scroll;
        position: relative;
        background-origin: padding-box;
        background-clip: border-box;
      }

      /* 页面头部 */
      .page-header {
        text-align: center;
        color: #2c3e50;
        position: relative;
        border-radius: 15px;
        padding: 20px 15px;
        margin: 0 auto 24px auto;
        max-width: 600px;
      }

      /* 标题容器 */
      .title-container {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        flex-direction: column;
      }

      /* Logo */
      .logo-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        box-shadow: 0 6px 20px rgba(74, 144, 226, 0.25);
        animation: logoFloat 3s ease-in-out infinite;
        position: relative;
        overflow: hidden;
        flex-shrink: 0;
        will-change: transform, box-shadow;
      }

      .logo-icon::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        animation: logoShine 4s ease-in-out infinite;
        z-index: 0;
      }

      .logo-icon img {
        width: 45px;
        height: 45px;
        object-fit: contain;
        z-index: 1;
        position: relative;
        filter: brightness(1.05);
        background: transparent;
        border-radius: 50%;
        max-width: 100%;
        max-height: 100%;
      }

      .page-title {
        font-size: 42px;
        font-weight: bold;
        margin: 0;
        position: relative;
        display: inline-block;
        letter-spacing: 2px;
      }

      .title-text {
        background: linear-gradient(45deg, #000000, #1e3a8a, #000000);
        background-size: 400% 400%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: gradientShift 4s ease-in-out infinite;
        position: relative;
        z-index: 2;
        text-shadow: 0 0 30px rgba(30, 58, 138, 0.6);
        filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
      }

      .title-glow {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, #4a90e2, #357abd);
        opacity: 0.2;
        filter: blur(15px);
        animation: pulse 2s ease-in-out infinite alternate;
        z-index: 1;
      }

      .title-particles {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        pointer-events: none;
        opacity: 0.8;
      }

      .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: #4a90e2;
        border-radius: 50%;
        opacity: 0;
        animation: float 3s ease-in-out infinite;
        box-shadow: 0 0 8px rgba(74, 145, 226, 0.9);
      }

      .particle:nth-child(1) {
        left: 10%;
        animation-delay: 0s;
      }
      .particle:nth-child(2) {
        left: 20%;
        animation-delay: 0.5s;
      }
      .particle:nth-child(3) {
        left: 80%;
        animation-delay: 1s;
      }
      .particle:nth-child(4) {
        left: 90%;
        animation-delay: 1.5s;
      }
      .particle:nth-child(5) {
        left: 30%;
        animation-delay: 2s;
      }
      .particle:nth-child(6) {
        left: 70%;
        animation-delay: 2.5s;
      }

      .page-subtitle {
        font-size: 18px;
        margin: 0;
        color: #000927;
        position: relative;
        font-weight: 400;
        letter-spacing: 1px;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
      }

      .subtitle-text {
        opacity: 0;
        animation: fadeInUp 1s ease-out 0.5s forwards;
      }

      /* 搜索区域 */
      .search-section {
        display: flex;
        justify-content: center;
        margin-bottom: 24px;
      }

      .search-container {
        position: relative;
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
      }

      .search-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #4a90e2;
        z-index: 3;
        transition: all 0.3s ease;
        font-size: 20px;
      }

      .search-input {
        width: 100%;
        height: 45px;
        border-radius: 22.5px;
        border: 2px solid rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.85);
        backdrop-filter: blur(15px);
        box-shadow: 0 8px 32px rgba(30, 58, 138, 0.1),
          0 4px 16px rgba(255, 255, 255, 0.3);
        font-size: 15px;
        padding-left: 45px;
        padding-right: 20px;
        color: #1e40af;
        transition: all 0.3s ease;
        outline: none;
      }

      .search-input:focus {
        border-color: #3b82f6;
        box-shadow: 0 8px 32px rgba(30, 58, 138, 0.2),
          0 0 0 3px rgba(59, 130, 246, 0.15);
        background: rgba(255, 255, 255, 0.95);
      }

      .search-input::placeholder {
        color: #7f8c8d;
        font-weight: 300;
      }

      /* 卡片网格 */
      .agent-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 16px;
      }

      /* 智能体卡片 */
      .agent-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 16px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.4);
        box-shadow: 0 8px 32px rgba(30, 58, 138, 0.08),
          0 4px 16px rgba(255, 255, 255, 0.25);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(12px);
        margin-bottom: 16px;
        break-inside: avoid;
      }

      .agent-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 12px 40px rgba(30, 58, 138, 0.12),
          0 8px 24px rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.95);
      }

      .agent-card.featured {
        position: relative;
        box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15),
          0 4px 16px rgba(255, 255, 255, 0.25);
      }

      .agent-card.featured::before {
        /* content: '推荐'; */
        position: absolute;
        top: 12px;
        right: 12px;
        background: #ff9500;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        z-index: 1;
      }

      /* 卡片头部 */
      .card-header {
        display: flex;
        align-items: center;
        padding: 12px 16px 10px 16px;
        border-bottom: 1px solid #f0f0f0;
      }

      .agent-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        flex-shrink: 0;
        font-size: 32px;
      }

      .agent-info {
        flex: 1;
        min-width: 0;
      }

      .agent-name {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #2c3e50;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .agent-category {
        font-size: 14px;
        color: #7f8c8d;
        margin: 0;
      }

      .agent-status {
        flex-shrink: 0;
      }

      .status-badge {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #52c41a;
      }

      /* 卡片内容 */
      .card-content {
        padding: 10px 16px;
      }

      .agent-description {
        font-size: 13px;
        line-height: 1.5;
        color: #5a6c7d;
        margin: 0 0 12px 0;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .agent-features {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
      }

      .feature-tag {
        font-size: 12px;
        border-radius: 12px;
        padding: 2px 8px;
        color: white;
        font-weight: 500;
      }

      .feature-tag.blue {
        background: #2d8cf0;
      }
      .feature-tag.green {
        background: #19be6b;
      }
      .feature-tag.orange {
        background: #ff9900;
      }
      .feature-tag.red {
        background: #ed4014;
      }
      .feature-tag.purple {
        background: #9c27b0;
      }

      /* 分页 */
      .pagination-wrapper {
        display: flex;
        justify-content: center;
        padding: 16px 0;
        margin-bottom: 30px;
      }

      .pagination {
        display: flex;
        gap: 8px;
        align-items: center;
        background: white;
        border-radius: 8px;
        padding: 12px 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .page-btn {
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .page-btn:hover {
        background: #f0f0f0;
      }

      .page-btn.active {
        background: #2d8cf0;
        color: white;
        border-color: #2d8cf0;
      }

      .page-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      /* 动画定义 */
      @keyframes logoFloat {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg) translateZ(0);
          box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
        }
        50% {
          transform: translateY(-8px) rotate(4deg) translateZ(0);
          box-shadow: 0 12px 30px rgba(74, 144, 226, 0.4);
        }
      }

      @keyframes logoShine {
        0% {
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
        50% {
          transform: translateX(100%) translateY(100%) rotate(45deg);
        }
        100% {
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
      }

      @keyframes gradientShift {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      @keyframes pulse {
        0% {
          opacity: 0.2;
          transform: scale(1);
        }
        100% {
          opacity: 0.4;
          transform: scale(1.05);
        }
      }

      @keyframes float {
        0%,
        100% {
          opacity: 0;
          transform: translateY(20px) scale(0);
        }
        50% {
          opacity: 0.8;
          transform: translateY(-20px) scale(1);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 隐藏类 */
      .hidden {
        display: none;
      }

      /* 聊天界面样式 */
      #chat-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1000;
        background: #fff;
      }

      .chat-overlay {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .chat-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .back-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
      }

      .back-btn:hover {
        background: #0056b3;
      }

      .chat-header h3 {
        margin: 0;
        color: #333;
        font-size: 18px;
      }

      .chat-content {
        flex: 1;
        overflow: hidden;
      }

      .chat-content dify-chat {
        width: 100%;
        height: 100%;
        display: block;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 智能体列表界面 -->
      <div class="agent-container" v-show="!showChat">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="title-container">
            <div class="logo-icon">
              <img src="./image/xingzai.png" alt="AI Logo" />
            </div>
            <h1 class="page-title">
              <span class="title-text">AI-兴仔</span>
              <span class="title-glow"></span>
              <div class="title-particles">
                <span class="particle"></span>
                <span class="particle"></span>
                <span class="particle"></span>
                <span class="particle"></span>
                <span class="particle"></span>
                <span class="particle"></span>
              </div>
            </h1>
          </div>
          <p class="page-subtitle">
            <span class="subtitle-text">AI智能体助手平台</span>
          </p>
        </div>

        <!-- 搜索栏 -->
        <div class="search-section">
          <div class="search-container">
            <div class="search-icon">🔍</div>
            <input
              type="text"
              v-model="searchKeyword"
              class="search-input"
              placeholder="搜索智能体..."
            />
          </div>
        </div>

        <!-- 智能体卡片列表 -->
        <div class="agent-grid">
          <div
            v-for="agent in currentAgents"
            :key="agent.id"
            :class="['agent-card', { featured: agent.featured }]"
            @click="selectAgent(agent)"
          >
            <div class="card-header">
              <div class="agent-info">
                <h3 class="agent-name">{{ agent.name }}</h3>
              </div>
              <div class="agent-status">
                <div class="status-badge"></div>
              </div>
            </div>
            <div class="card-content">
              <p class="agent-description">{{ agent.description }}</p>
              <div class="agent-features">
                <span
                  v-for="feature in agent.features"
                  :key="feature"
                  :class="['feature-tag', getFeatureColor(feature)]"
                >
                  {{ feature }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-show="totalPages > 1">
          <div class="pagination">
            <!-- 上一页按钮 -->
            <button
              class="page-btn"
              :disabled="currentPage === 1"
              @click="changePage(currentPage - 1)"
            >
              上一页
            </button>

            <!-- 页码按钮 -->
            <button
              v-for="page in totalPages"
              :key="page"
              :class="['page-btn', { active: page === currentPage }]"
              @click="changePage(page)"
            >
              {{ page }}
            </button>

            <!-- 下一页按钮 -->
            <button
              class="page-btn"
              :disabled="currentPage === totalPages"
              @click="changePage(currentPage + 1)"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 聊天界面 -->
      <div id="chat-container" v-show="showChat" style="display: none">
        <div class="chat-overlay">
          <!-- <div class="chat-header">
                <button class="back-btn" @click="goBackToAgents">← 返回智能体列表</button>
                <h3>{{ currentAgentName }}</h3>
            </div> -->
          <div class="chat-content">
            <dify-chat
              v-if="showChat && currentApiKey"
              :key="currentApiKey"
              :title="currentAgentName"
              api-url="http://*************:8080/v1"
              :api-key="currentApiKey"
              user="8392EA9256FD4788B3D27ED76F707323"
              post-name="前端工程师"
              @go-agent="goBackToAgents"
            >
            </dify-chat>
          </div>
        </div>
      </div>
    </div>

    <!-- 引入Vue2 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>

    <!-- 引入Dify Chat组件 -->
    <script src="lib/dify-chat.js"></script>

    <script>
      // Vue应用实例
      new Vue({
        el: "#app",
        data: {
          // 界面状态
          showChat: false,
          currentApiKey: "",
          currentAgentName: "",

          // 智能体数据
          agents: [
            {
              id: 1,
              name: "船舶营运助手",
              category: "船舶营运",
              description:
                "提供船舶营运管理服务，包括船舶信息查询、船期预测、AIS数据分析等功能，提升船舶营运效率，降低成本。",
              icon: "🚢",
              iconColor: "#2d8cf0",
              status: "active",
              featured: true,
              features: ["船舶信息", "船期预测", "AIS数据"],
              apiKey: "app-lrt9TpLGzouDkbx8HBfJDgDc",
            },
            {
              id: 2,
              name: "企规助理",
              category: "知识库",
              description:
                "专为企业制度建设打造的智能助手，随时随地为您提供制度查询、流程说明与合规指引。",
              icon: "📚",
              iconColor: "#ff9900",
              status: "active",
              featured: true,
              features: ["文档检索", "知识问答", "内容管理"],
              apiKey: "app-Eat37fo3FqnBMzVL0o7gOENK",
            },
            {
              id: 3,
              name: "国际市场研报分析",
              category: "知识库",
              description:
                "专注于全球市场动态解析与行业趋势洞察。通过对权威机构发布的市场研究报告、数据资料及政策环境的深度理解，提供精准的分析，辅助您快速掌握国际市场脉动，优化决策效率。",
              icon: "📊",
              iconColor: "#5A3EBA",
              status: "active",
              featured: true,
              features: ["文档检索", "知识问答", "内容管理"],
              apiKey: "app-Wrpx0J1NkU4uhHyKp5MQxaKS",
            },
            {
              id: 4,
              name: "国际海事法规与操作",
              category: "知识库",
              description:
                "提供法规条文查询、操作规范解读、合规流程指导等功能，帮助用户快速获取和理解IMO相关国际公约及实务要求，提升船舶管理的合规性与操作效率。",
              icon: "🌊",
              iconColor: "#1A3E5F",
              status: "active",
              featured: true,
              features: ["文档检索", "知识问答", "内容管理"],
              apiKey: "app-EwiYo70xwBXyn4GfKvDEdrlZ",
            },
            {
              id: 5,
              name: "体系助手",
              category: "知识库",
              description:
                "企业内部用于支持管理体系文件查询与执行的AI智能助手。 它集成了应急手册、安全管理手册、程序文件、船舶操作规范、船岸记录等核心制度文档，支持智能问答、快速检索与内容理解，助力员工高效获取制度信息，确保各岗位规范操作、合规执行。",
              icon: "🏢",
              iconColor: "#1E3A8A",
              status: "active",
              featured: true,
              features: ["文档检索", "知识问答", "内容管理"],
              apiKey: "app-8qYgCHOFzOqZ39sZempNPmTY",
            },
            {
              id: 6,
              name: "法律法规",
              category: "知识库",
              description:
                "提供法律法规条文查询、操作规范检索、合规流程指引等功能，覆盖国家法律、海事法规及安全管理制度，帮助用户高效获取权威信息，强化制度执行，提升合规管理与操作效率。",
              icon: "⚖️",
              iconColor: "#5A3EBA",
              status: "active",
              featured: true,
              features: ["文档检索", "知识问答", "内容管理"],
              apiKey: "app-onkWxspn0H0OqVwTKfQ8TGOJ",
            },
            {
              id: 7,
              name: "企业信息查询",
              category: "工具",
              description:
                "致力于为您提供企业相关信息的智能化查询服务。 无论是企业名称、注册资本、法人代表，还是企业状态、经营范围，您都可以随时向我提问，快速定位所需信息，助您高效研判、决策有据。",
              icon: "🏭",
              iconColor: "#1677FF",
              status: "active",
              featured: true,
              features: ["工具查询", "企业信息", "企业资产"],
              apiKey: "app-tMbkvtf7PWGmGVVpNJ7EaO18",
            },
          ],

          // 分页和搜索状态
          searchKeyword: "",
          currentPage: 1,
          pageSize: 12,
        },

        computed: {
          // 过滤后的智能体
          filteredAgents() {
            if (!this.searchKeyword) {
              return this.agents;
            }
            return this.agents.filter(
              (agent) =>
                agent.name
                  .toLowerCase()
                  .includes(this.searchKeyword.toLowerCase()) ||
                agent.description
                  .toLowerCase()
                  .includes(this.searchKeyword.toLowerCase()) ||
                agent.category
                  .toLowerCase()
                  .includes(this.searchKeyword.toLowerCase())
            );
          },
          // 当前页的智能体
          currentAgents() {
            const start = (this.currentPage - 1) * this.pageSize;
            const end = start + this.pageSize;
            return this.filteredAgents.slice(start, end);
          },
          // 总页数
          totalPages() {
            return Math.ceil(this.filteredAgents.length / this.pageSize);
          },
        },
        watch: {
          // 监听搜索关键词变化
          searchKeyword() {
            this.currentPage = 1;
          },
          // 监听搜索关键词变化
          searchKeyword() {
            this.currentPage = 1;
          },
        },
        methods: {
          // 获取特征标签颜色
          getFeatureColor(feature) {
            const colors = ["blue", "green", "orange", "red", "purple"];
            const index = feature.length % colors.length;
            return colors[index];
          },
          // 选择智能体
          selectAgent(agent) {
            console.log("选择智能体:", agent.name, "API Key:", agent.apiKey);
            this.currentApiKey = agent.apiKey;
            this.currentAgentName = agent.name;
            this.showChat = true;

            // 确保dify-chat组件能正确初始化
            this.$nextTick(() => {
              console.log("聊天界面已显示，dify-chat组件应该开始初始化");
            });
          },
          // 返回智能体列表（对应chat.html中的goAgent方法）
          goBackToAgents() {
            console.log("返回智能体列表");
            this.showChat = false;
            this.currentApiKey = "";
            this.currentAgentName = "";
          },
          // 切换页面
          changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
              this.currentPage = page;
              // 滚动到顶部
              window.scrollTo({ top: 0, behavior: "smooth" });
            }
          },
          // 搜索处理
          handleSearch(event) {
            this.searchKeyword = event.target.value.trim();
          },
        },
        mounted() {
          // qing.call("getPersonInfo", {
          //   // 针对云之家小程序限定
          //   success: function (res) {
          //     if (res.data.openId && res.data.openId !== "") {
          //       this.openId = res.data.openId;
          //     }
          //   },
          //   error: function (err) {
          //     console.log(err);
          //   },
          // });

          // POST请求获取部门账号菜单配置列表
          const postData = {
            openId: '632cfe9be4b00130127ee36c',
            idPath: '>77>79>'
          };

          // 使用fetch with mode: 'no-cors' 或者使用XMLHttpRequest
          fetch('http://192.168.1.50:8059/basic/menu/config/queryDeptAccountMenuConfigList', {
            method: 'POST',
            mode: 'cors', // 尝试CORS
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type'
            },
            body: JSON.stringify(postData)
          })
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            console.log('API响应数据:', data);
          })
          .catch(error => {
            console.error('Fetch请求失败，尝试使用XMLHttpRequest:', error);

            // 如果fetch失败，尝试使用XMLHttpRequest
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'http://192.168.1.50:8059/basic/menu/config/queryDeptAccountMenuConfigList', true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
              if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                  try {
                    const data = JSON.parse(xhr.responseText);
                    console.log('XMLHttpRequest API响应数据:', data);
                  } catch (e) {
                    console.log('XMLHttpRequest API响应文本:', xhr.responseText);
                  }
                } else {
                  console.error('XMLHttpRequest请求失败:', xhr.status, xhr.statusText);
                }
              }
            };

            xhr.onerror = function() {
              console.error('XMLHttpRequest网络错误');
            };

            xhr.send(JSON.stringify(postData));
          });
        },
      });
    </script>
  </body>
</html>
